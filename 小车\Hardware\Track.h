#ifndef __TRACK_H
#define __TRACK_H

void Infrared_Init(void);
void track_start(void);

#define track_1 GPIO_ReadInputDataBit(GPIOB, GPIO_Pin_6)
#define track_2 GPIO_ReadInputDataBit(GPIOB, GPIO_Pin_3)
#define track_3 GPIO_ReadInputDataBit(GPIOB, GPIO_Pin_4)
#define track_4 GPIO_ReadInputDataBit(GPIOB, GPIO_Pin_5)
#define track_5 GPIO_ReadInputDataBit(GPIOB, GPIO_Pin_8)
#define track_6 GPIO_ReadInputDataBit(GPIOB, GPIO_Pin_7)
#define track_7 GPIO_ReadInputDataBit(GPIOB, GPIO_Pin_9)

#endif
